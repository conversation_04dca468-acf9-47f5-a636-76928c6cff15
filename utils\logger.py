"""
Logging utility module for Google Ads Keyword Research Tool
"""
import logging
import os
from datetime import datetime
from typing import Optional
from config.config import AppSettings


class Logger:
    """Centralized logging manager"""
    
    def __init__(self, name: str = "KeywordResearch", log_file: Optional[str] = None):
        """
        Initialize logger
        
        Args:
            name (str): Logger name
            log_file (str, optional): Log file path
        """
        self.name = name
        self.log_file = log_file or AppSettings.LOG_FILENAME
        self._logger = None
        self._setup_logger()
    
    def _setup_logger(self) -> None:
        """Setup logger with file and console handlers"""
        self._logger = logging.getLogger(self.name)
        self._logger.setLevel(logging.INFO)
        
        # Clear existing handlers
        self._logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            AppSettings.LOG_FORMAT,
            datefmt=AppSettings.LOG_DATE_FORMAT
        )
        
        # File handler
        try:
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(formatter)
            self._logger.addHandler(file_handler)
        except Exception as e:
            print(f"Warning: Could not create log file handler: {e}")
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self._logger.addHandler(console_handler)
    
    def info(self, message: str) -> None:
        """Log info message"""
        self._logger.info(message)
    
    def warning(self, message: str) -> None:
        """Log warning message"""
        self._logger.warning(message)
    
    def error(self, message: str) -> None:
        """Log error message"""
        self._logger.error(message)
    
    def debug(self, message: str) -> None:
        """Log debug message"""
        self._logger.debug(message)
    
    def exception(self, message: str) -> None:
        """Log exception with traceback"""
        self._logger.exception(message)
    
    @property
    def logger(self) -> logging.Logger:
        """Get the underlying logger instance"""
        return self._logger


# Global logger instance
app_logger = Logger()


class GUILogger:
    """Logger that can also output to GUI components"""
    
    def __init__(self, text_widget=None, logger_instance: Optional[Logger] = None):
        """
        Initialize GUI logger
        
        Args:
            text_widget: Tkinter text widget for GUI output
            logger_instance: Logger instance to use
        """
        self.text_widget = text_widget
        self.logger = logger_instance or app_logger
    
    def log_to_gui(self, message: str, level: str = "INFO") -> None:
        """
        Log message to GUI text widget
        
        Args:
            message (str): Message to log
            level (str): Log level
        """
        if self.text_widget:
            timestamp = datetime.now().strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {level}: {message}\n"
            
            try:
                self.text_widget.insert("end", formatted_message)
                self.text_widget.see("end")
                self.text_widget.update()
            except Exception:
                pass  # Ignore GUI update errors
    
    def info(self, message: str) -> None:
        """Log info message to both logger and GUI"""
        self.logger.info(message)
        self.log_to_gui(message, "INFO")
    
    def warning(self, message: str) -> None:
        """Log warning message to both logger and GUI"""
        self.logger.warning(message)
        self.log_to_gui(message, "WARNING")
    
    def error(self, message: str) -> None:
        """Log error message to both logger and GUI"""
        self.logger.error(message)
        self.log_to_gui(message, "ERROR")
    
    def success(self, message: str) -> None:
        """Log success message to both logger and GUI"""
        self.logger.info(f"SUCCESS: {message}")
        self.log_to_gui(message, "SUCCESS")
