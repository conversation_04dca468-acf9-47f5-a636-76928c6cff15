"""
Input frame for Google Ads Keyword Research Tool
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from typing import Callable, Optional, Tuple, Dict, Any

from config.config import AppSettings


class InputFrame(ttk.LabelFrame):
    """Input frame for keyword research parameters"""
    
    def __init__(self, parent, on_generate_callback: Callable):
        """
        Initialize input frame
        
        Args:
            parent: Parent widget
            on_generate_callback: Callback function for generate button
        """
        super().__init__(parent, text="Keyword Research Parameters", padding="10")
        
        self.on_generate_callback = on_generate_callback
        self.create_widgets()
    
    def create_widgets(self) -> None:
        """Create and layout input widgets"""
        # Configure grid weights
        self.grid_columnconfigure(1, weight=1)
        
        row = 0
        
        # Customer ID
        ttk.Label(self, text="Customer ID:").grid(row=row, column=0, sticky="w", pady=2)
        self.customer_id_var = tk.StringVar()
        customer_id_entry = ttk.Entry(self, textvariable=self.customer_id_var, width=30)
        customer_id_entry.grid(row=row, column=1, sticky="ew", padx=(10, 0), pady=2)
        
        row += 1
        
        # Keywords
        ttk.Label(self, text="Keywords:").grid(row=row, column=0, sticky="nw", pady=2)
        keywords_frame = ttk.Frame(self)
        keywords_frame.grid(row=row, column=1, sticky="ew", padx=(10, 0), pady=2)
        keywords_frame.grid_columnconfigure(0, weight=1)
        
        self.keywords_text = tk.Text(keywords_frame, height=4, width=40, wrap=tk.WORD)
        keywords_scrollbar = ttk.Scrollbar(keywords_frame, orient="vertical", command=self.keywords_text.yview)
        self.keywords_text.configure(yscrollcommand=keywords_scrollbar.set)
        
        self.keywords_text.grid(row=0, column=0, sticky="ew")
        keywords_scrollbar.grid(row=0, column=1, sticky="ns")
        
        # Keywords help text
        keywords_help = ttk.Label(self, text="Enter keywords one per line or comma-separated", 
                                 font=("TkDefaultFont", 8), foreground="gray")
        keywords_help.grid(row=row+1, column=1, sticky="w", padx=(10, 0))
        
        row += 2
        
        # Page URL
        ttk.Label(self, text="Page URL:").grid(row=row, column=0, sticky="w", pady=2)
        self.page_url_var = tk.StringVar()
        page_url_entry = ttk.Entry(self, textvariable=self.page_url_var, width=50)
        page_url_entry.grid(row=row, column=1, sticky="ew", padx=(10, 0), pady=2)
        
        row += 1
        
        # Location IDs
        ttk.Label(self, text="Location IDs:").grid(row=row, column=0, sticky="nw", pady=2)
        location_frame = ttk.Frame(self)
        location_frame.grid(row=row, column=1, sticky="ew", padx=(10, 0), pady=2)
        location_frame.grid_columnconfigure(0, weight=1)
        
        self.location_ids_text = tk.Text(location_frame, height=3, width=40, wrap=tk.WORD)
        location_scrollbar = ttk.Scrollbar(location_frame, orient="vertical", command=self.location_ids_text.yview)
        self.location_ids_text.configure(yscrollcommand=location_scrollbar.set)
        
        self.location_ids_text.grid(row=0, column=0, sticky="ew")
        location_scrollbar.grid(row=0, column=1, sticky="ns")
        
        # Location help text
        location_help = ttk.Label(self, text="Enter location IDs one per line or comma-separated (optional)", 
                                 font=("TkDefaultFont", 8), foreground="gray")
        location_help.grid(row=row+1, column=1, sticky="w", padx=(10, 0))
        
        row += 2
        
        # Language ID
        ttk.Label(self, text="Language ID:").grid(row=row, column=0, sticky="w", pady=2)
        self.language_id_var = tk.StringVar(value=AppSettings.DEFAULT_LANGUAGE_ID)
        language_id_entry = ttk.Entry(self, textvariable=self.language_id_var, width=20)
        language_id_entry.grid(row=row, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # Language help text
        language_help = ttk.Label(self, text="1000 = English (default)", 
                                 font=("TkDefaultFont", 8), foreground="gray")
        language_help.grid(row=row, column=2, sticky="w", padx=(10, 0))
        
        row += 1
        
        # Output file
        ttk.Label(self, text="Output File:").grid(row=row, column=0, sticky="w", pady=2)
        output_frame = ttk.Frame(self)
        output_frame.grid(row=row, column=1, sticky="ew", padx=(10, 0), pady=2)
        output_frame.grid_columnconfigure(0, weight=1)
        
        self.output_file_var = tk.StringVar(value=AppSettings.DEFAULT_OUTPUT_FILENAME)
        output_file_entry = ttk.Entry(output_frame, textvariable=self.output_file_var)
        output_file_entry.grid(row=0, column=0, sticky="ew", padx=(0, 5))
        
        browse_button = ttk.Button(output_frame, text="Browse...", command=self.browse_output_file)
        browse_button.grid(row=0, column=1)
        
        row += 1
        
        # Generate button
        button_frame = ttk.Frame(self)
        button_frame.grid(row=row, column=0, columnspan=3, pady=(20, 0))
        
        self.generate_button = ttk.Button(
            button_frame, 
            text="Generate Keyword Ideas", 
            command=self.on_generate_callback,
            style="Accent.TButton"
        )
        self.generate_button.pack()
    
    def browse_output_file(self) -> None:
        """Open file dialog to select output file"""
        filename = filedialog.asksaveasfilename(
            title="Select Output File",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            initialfilename=self.output_file_var.get()
        )
        
        if filename:
            self.output_file_var.set(filename)
    
    def get_request_data(self) -> Optional[Dict[str, Any]]:
        """
        Get request data from input fields
        
        Returns:
            Dictionary with request data or None if validation fails
        """
        try:
            # Get keywords
            keywords_text = self.keywords_text.get("1.0", tk.END).strip()
            keywords = []
            if keywords_text:
                # Split by lines first, then by commas
                for line in keywords_text.split('\n'):
                    line = line.strip()
                    if line:
                        if ',' in line:
                            keywords.extend([kw.strip() for kw in line.split(',') if kw.strip()])
                        else:
                            keywords.append(line)
            
            # Get location IDs
            location_text = self.location_ids_text.get("1.0", tk.END).strip()
            location_ids = []
            if location_text:
                # Split by lines first, then by commas
                for line in location_text.split('\n'):
                    line = line.strip()
                    if line:
                        if ',' in line:
                            location_ids.extend([loc.strip() for loc in line.split(',') if loc.strip()])
                        else:
                            location_ids.append(line)
            
            return {
                'customer_id': self.customer_id_var.get().strip(),
                'keywords': keywords,
                'page_url': self.page_url_var.get().strip() or None,
                'location_ids': location_ids,
                'language_id': self.language_id_var.get().strip(),
                'output_file': self.output_file_var.get().strip()
            }
            
        except Exception as e:
            messagebox.showerror("Error", f"Error getting input data: {str(e)}")
            return None
    
    def validate_inputs(self) -> Tuple[bool, str]:
        """
        Validate input fields
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            data = self.get_request_data()
            if not data:
                return False, "Failed to get input data"
            
            # Validate customer ID
            if not data['customer_id']:
                return False, "Customer ID is required"
            
            # Validate that either keywords or URL is provided
            if not data['keywords'] and not data['page_url']:
                return False, "Either keywords or page URL must be provided"
            
            # Validate language ID
            if not data['language_id']:
                return False, "Language ID is required"
            
            try:
                int(data['language_id'])
            except ValueError:
                return False, "Language ID must be a number"
            
            # Validate output file
            if not data['output_file']:
                return False, "Output file path is required"
            
            if not data['output_file'].lower().endswith('.csv'):
                return False, "Output file must have .csv extension"
            
            # Validate location IDs if provided
            for location_id in data['location_ids']:
                try:
                    int(location_id)
                except ValueError:
                    return False, f"Invalid location ID: {location_id}. Location IDs must be numbers."
            
            return True, ""
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    def set_enabled(self, enabled: bool) -> None:
        """
        Enable or disable input controls
        
        Args:
            enabled: Whether to enable controls
        """
        state = "normal" if enabled else "disabled"
        
        # Update button state
        self.generate_button.configure(state=state)
        
        # Note: Text widgets and Entry widgets would need individual handling
        # This is a simplified version
