#!/usr/bin/env python3
"""
Google Ads Keyword Research Tool - Main Application Entry Point

A professional GUI application for generating keyword ideas using the Google Ads API.
Features include OAuth2 authentication, keyword research with multiple seed types,
data sorting, and CSV export functionality.

Author: AI Assistant
Version: 1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from gui.main_window import MainWindow
    from utils.logger import app_logger
    from config.config import AppSettings
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required dependencies are installed.")
    print("Run: pip install -r requirements.txt")
    sys.exit(1)


def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    try:
        import google.ads.googleads
    except ImportError:
        missing_deps.append("google-ads")
    
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        import yaml
    except ImportError:
        missing_deps.append("pyyaml")
    
    if missing_deps:
        error_msg = f"Missing required dependencies: {', '.join(missing_deps)}\n"
        error_msg += "Please install them using: pip install -r requirements.txt"
        print(error_msg)
        
        # Try to show GUI error if tkinter is available
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Missing Dependencies", error_msg)
        except:
            pass
        
        return False
    
    return True


def check_config_file():
    """Check if the Google Ads configuration file exists"""
    config_file = "google-ads.yaml"
    
    if not os.path.exists(config_file):
        error_msg = f"Configuration file '{config_file}' not found.\n"
        error_msg += "Please ensure the file exists and contains your Google Ads API credentials."
        
        print(error_msg)
        
        # Try to show GUI error
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Configuration Error", error_msg)
        except:
            pass
        
        return False
    
    return True


def setup_error_handling():
    """Setup global error handling"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        """Handle uncaught exceptions"""
        if issubclass(exc_type, KeyboardInterrupt):
            # Allow keyboard interrupt to work normally
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # Log the error
        error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        app_logger.error(f"Uncaught exception: {error_msg}")
        
        # Show error dialog
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror(
                "Unexpected Error",
                f"An unexpected error occurred:\n\n{exc_value}\n\nCheck the log file for details."
            )
        except:
            print(f"Fatal error: {exc_value}")
    
    sys.excepthook = handle_exception


def main():
    """Main application entry point"""
    try:
        # Setup error handling
        setup_error_handling()
        
        # Check dependencies
        if not check_dependencies():
            return 1
        
        # Check configuration file
        if not check_config_file():
            return 1
        
        # Log application startup
        app_logger.info("=" * 50)
        app_logger.info("Google Ads Keyword Research Tool Starting")
        app_logger.info("=" * 50)
        
        # Create and run the main application
        app = MainWindow()
        app.run()
        
        app_logger.info("Application closed normally")
        return 0
        
    except Exception as e:
        error_msg = f"Failed to start application: {str(e)}"
        print(error_msg)
        app_logger.error(error_msg)
        
        # Show error dialog
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Startup Error", error_msg)
        except:
            pass
        
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
