#!/usr/bin/env python3
"""
Test script to verify the Google Ads Keyword Research Tool setup
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import tkinter
        print("✓ tkinter available")
    except ImportError:
        print("✗ tkinter not available")
        return False
    
    try:
        import google.ads.googleads
        print("✓ google-ads library available")
    except ImportError:
        print("✗ google-ads library not available")
        print("  Run: pip install google-ads")
        return False
    
    try:
        import pandas
        print("✓ pandas available")
    except ImportError:
        print("✗ pandas not available")
        print("  Run: pip install pandas")
        return False
    
    try:
        import yaml
        print("✓ pyyaml available")
    except ImportError:
        print("✗ pyyaml not available")
        print("  Run: pip install pyyaml")
        return False
    
    return True

def test_config_file():
    """Test if configuration file exists and is valid"""
    print("\nTesting configuration...")
    
    config_file = "google-ads.yaml"
    if not os.path.exists(config_file):
        print(f"✗ Configuration file '{config_file}' not found")
        return False
    
    try:
        import yaml
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        required_fields = ['developer_token', 'client_id', 'client_secret', 'refresh_token']
        missing_fields = []
        
        for field in required_fields:
            if field not in config or not config[field]:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"✗ Missing required fields in config: {', '.join(missing_fields)}")
            return False
        
        print("✓ Configuration file is valid")
        return True
        
    except Exception as e:
        print(f"✗ Error reading configuration file: {e}")
        return False

def test_project_structure():
    """Test if project structure is correct"""
    print("\nTesting project structure...")
    
    required_dirs = ['config', 'models', 'services', 'gui', 'utils']
    required_files = [
        'main.py',
        'requirements.txt',
        'config/config.py',
        'models/keyword_data.py',
        'services/google_ads_client.py',
        'services/csv_exporter.py',
        'services/keyword_research_service.py',
        'gui/main_window.py',
        'gui/input_frame.py',
        'gui/output_frame.py',
        'utils/logger.py'
    ]
    
    missing_items = []
    
    for directory in required_dirs:
        if not os.path.isdir(directory):
            missing_items.append(f"Directory: {directory}")
    
    for file_path in required_files:
        if not os.path.isfile(file_path):
            missing_items.append(f"File: {file_path}")
    
    if missing_items:
        print("✗ Missing project components:")
        for item in missing_items:
            print(f"  - {item}")
        return False
    
    print("✓ Project structure is correct")
    return True

def test_module_imports():
    """Test if custom modules can be imported"""
    print("\nTesting custom module imports...")
    
    try:
        from config.config import Config, AppSettings
        print("✓ Config modules imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import config modules: {e}")
        return False
    
    try:
        from models.keyword_data import KeywordIdea, KeywordResearchRequest
        print("✓ Model modules imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import model modules: {e}")
        return False
    
    try:
        from utils.logger import Logger, GUILogger
        print("✓ Logger modules imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import logger modules: {e}")
        return False
    
    return True

def main():
    """Run all tests"""
    print("Google Ads Keyword Research Tool - Setup Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_project_structure,
        test_module_imports,
        test_config_file
    ]
    
    all_passed = True
    
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ All tests passed! The application should work correctly.")
        print("Run 'python main.py' to start the application.")
    else:
        print("✗ Some tests failed. Please fix the issues before running the application.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
