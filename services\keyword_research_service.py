"""
Main keyword research service that orchestrates the entire process
"""
from typing import Dict, Any, List, Optional

from config.config import Config
from models.keyword_data import KeywordResearchRequest, KeywordIdea
from services.google_ads_client import GoogleAdsService
from services.csv_exporter import CSVExporter
from utils.logger import GUILogger


class KeywordResearchService:
    """Main service for keyword research operations"""
    
    def __init__(self, logger: Optional[GUILogger] = None):
        """
        Initialize keyword research service
        
        Args:
            logger: GUI logger instance
        """
        self.logger = logger or GUILogger()
        self.config = None
        self.google_ads_service = None
        self.csv_exporter = None
        
        self._initialize_services()
    
    def _initialize_services(self) -> None:
        """Initialize all required services"""
        try:
            # Load configuration
            self.logger.info("Loading configuration...")
            self.config = Config()
            self.logger.info("Configuration loaded successfully")
            
            # Initialize Google Ads service
            self.logger.info("Initializing Google Ads service...")
            self.google_ads_service = GoogleAdsService(self.config, self.logger.logger)
            self.logger.info("Google Ads service initialized successfully")
            
            # Initialize CSV exporter
            self.csv_exporter = CSVExporter(self.logger.logger)
            self.logger.info("CSV exporter initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize services: {str(e)}")
            raise
    
    def generate_keywords(self, request_data: Dict[str, Any]) -> bool:
        """
        Generate keyword ideas and export to CSV
        
        Args:
            request_data: Dictionary containing request parameters
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create request object
            self.logger.info("Creating keyword research request...")
            request = self._create_request(request_data)
            
            # Validate request
            self._validate_request(request)
            
            # Generate keyword ideas
            self.logger.info(f"Generating keyword ideas for customer {request.customer_id}...")
            keywords = self.google_ads_service.generate_keyword_ideas(request)
            
            if not keywords:
                self.logger.warning("No keyword ideas were generated")
                return False
            
            self.logger.info(f"Generated {len(keywords)} keyword ideas")
            
            # Export to CSV
            self.logger.info(f"Exporting keywords to {request.output_file}...")
            success = self.csv_exporter.export_keywords(keywords, request.output_file)
            
            if success:
                self.logger.success(f"Successfully exported {len(keywords)} keywords to {request.output_file}")
                return True
            else:
                self.logger.error("Failed to export keywords to CSV")
                return False
                
        except Exception as e:
            self.logger.error(f"Error during keyword generation: {str(e)}")
            return False
    
    def _create_request(self, request_data: Dict[str, Any]) -> KeywordResearchRequest:
        """
        Create KeywordResearchRequest from input data
        
        Args:
            request_data: Dictionary containing request parameters
            
        Returns:
            KeywordResearchRequest object
        """
        try:
            return KeywordResearchRequest(
                customer_id=request_data['customer_id'],
                keywords=request_data['keywords'],
                page_url=request_data.get('page_url'),
                location_ids=request_data.get('location_ids', []),
                language_id=request_data.get('language_id', '1000'),
                output_file=request_data['output_file']
            )
        except Exception as e:
            raise ValueError(f"Invalid request data: {str(e)}")
    
    def _validate_request(self, request: KeywordResearchRequest) -> None:
        """
        Validate the keyword research request
        
        Args:
            request: KeywordResearchRequest to validate
            
        Raises:
            ValueError: If validation fails
        """
        # Validate customer ID
        if not request.customer_id:
            raise ValueError("Customer ID is required")
        
        # Validate that either keywords or URL is provided
        if not request.has_keywords and not request.has_url:
            raise ValueError("Either keywords or page URL must be provided")
        
        # Validate output file
        is_valid, error_message = self.csv_exporter.validate_output_path(request.output_file)
        if not is_valid:
            raise ValueError(f"Invalid output file: {error_message}")
        
        # Log request details
        self.logger.info(f"Request validation successful:")
        self.logger.info(f"  - Customer ID: {request.customer_id}")
        self.logger.info(f"  - Keywords: {len(request.keywords)} provided")
        self.logger.info(f"  - Page URL: {'Yes' if request.has_url else 'No'}")
        self.logger.info(f"  - Location IDs: {len(request.location_ids)} provided")
        self.logger.info(f"  - Language ID: {request.language_id}")
        self.logger.info(f"  - Seed type: {request.seed_type}")
        self.logger.info(f"  - Output file: {request.output_file}")
    
    def test_connection(self) -> bool:
        """
        Test the Google Ads API connection
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.logger.info("Testing Google Ads API connection...")
            
            # Try to access the client
            if self.google_ads_service and self.google_ads_service._client:
                self.logger.success("Google Ads API connection test successful")
                return True
            else:
                self.logger.error("Google Ads client not initialized")
                return False
                
        except Exception as e:
            self.logger.error(f"Google Ads API connection test failed: {str(e)}")
            return False
    
    def get_config_info(self) -> Dict[str, Any]:
        """
        Get configuration information
        
        Returns:
            Dictionary with configuration details
        """
        if not self.config:
            return {}
        
        return {
            'developer_token': self.config.developer_token[:10] + "..." if self.config.developer_token else "Not set",
            'client_id': self.config.client_id[:20] + "..." if self.config.client_id else "Not set",
            'has_refresh_token': bool(self.config.refresh_token),
            'use_proto_plus': self.config.use_proto_plus,
            'login_customer_id': self.config.login_customer_id or "Not set"
        }
    
    def validate_output_file(self, output_file: str) -> tuple[bool, str]:
        """
        Validate output file path
        
        Args:
            output_file: Path to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if self.csv_exporter:
            return self.csv_exporter.validate_output_path(output_file)
        return False, "CSV exporter not initialized"
