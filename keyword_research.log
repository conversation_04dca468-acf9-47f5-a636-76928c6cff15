2025-07-29 16:06:08 - KeywordResearch - INFO - ==================================================
2025-07-29 16:06:08 - KeywordResearch - INFO - Google Ads Keyword Research Tool Starting
2025-07-29 16:06:08 - KeywordResearch - INFO - ==================================================
2025-07-29 16:06:09 - KeywordResearch - INFO - Loading configuration...
2025-07-29 16:06:09 - KeywordResearch - INFO - Configuration loaded successfully
2025-07-29 16:06:09 - KeywordResearch - INFO - Initializing Google Ads service...
2025-07-29 16:06:09 - KeywordResearch - INFO - Google Ads client initialized successfully
2025-07-29 16:06:09 - KeywordResearch - INFO - Google Ads service initialized successfully
2025-07-29 16:06:09 - KeywordResearch - INFO - CSV exporter initialized successfully
2025-07-29 16:06:09 - KeywordResearch - INFO - Application initialized successfully
2025-07-29 16:07:25 - KeywordResearch - INFO - ==================================================
2025-07-29 16:07:25 - KeywordResearch - INFO - Google Ads Keyword Research Tool Starting
2025-07-29 16:07:25 - KeywordResearch - INFO - ==================================================
2025-07-29 16:07:25 - KeywordResearch - INFO - Loading configuration...
2025-07-29 16:07:25 - KeywordResearch - INFO - Configuration loaded successfully
2025-07-29 16:07:25 - KeywordResearch - INFO - Initializing Google Ads service...
2025-07-29 16:07:25 - KeywordResearch - INFO - Google Ads client initialized successfully
2025-07-29 16:07:25 - KeywordResearch - INFO - Google Ads service initialized successfully
2025-07-29 16:07:25 - KeywordResearch - INFO - CSV exporter initialized successfully
2025-07-29 16:07:25 - KeywordResearch - INFO - Application initialized successfully
2025-07-29 16:07:52 - KeywordResearch - INFO - Application closing...
2025-07-29 16:07:52 - KeywordResearch - INFO - Application closed normally
2025-07-29 16:08:17 - KeywordResearch - INFO - ==================================================
2025-07-29 16:08:17 - KeywordResearch - INFO - Google Ads Keyword Research Tool Starting
2025-07-29 16:08:17 - KeywordResearch - INFO - ==================================================
2025-07-29 16:08:17 - KeywordResearch - INFO - Loading configuration...
2025-07-29 16:08:17 - KeywordResearch - INFO - Configuration loaded successfully
2025-07-29 16:08:17 - KeywordResearch - INFO - Initializing Google Ads service...
2025-07-29 16:08:17 - KeywordResearch - INFO - Google Ads client initialized successfully
2025-07-29 16:08:17 - KeywordResearch - INFO - Google Ads service initialized successfully
2025-07-29 16:08:17 - KeywordResearch - INFO - CSV exporter initialized successfully
2025-07-29 16:08:17 - KeywordResearch - INFO - Application initialized successfully
2025-07-29 16:09:48 - KeywordResearch - INFO - Starting keyword generation...
2025-07-29 16:09:48 - KeywordResearch - INFO - Creating keyword research request...
2025-07-29 16:09:48 - KeywordResearch - INFO - Request validation successful:
2025-07-29 16:09:48 - KeywordResearch - INFO -   - Customer ID: 138-688-4705
2025-07-29 16:09:48 - KeywordResearch - INFO -   - Keywords: 1 provided
2025-07-29 16:09:48 - KeywordResearch - INFO -   - Page URL: No
2025-07-29 16:09:48 - KeywordResearch - INFO -   - Location IDs: 1 provided
2025-07-29 16:09:48 - KeywordResearch - INFO -   - Language ID: 1000
2025-07-29 16:09:48 - KeywordResearch - INFO -   - Seed type: keyword_seed
2025-07-29 16:09:48 - KeywordResearch - INFO -   - Output file: keyword_ideas.csv
2025-07-29 16:09:48 - KeywordResearch - INFO - Generating keyword ideas for customer 138-688-4705...
2025-07-29 16:09:48 - KeywordResearch - INFO - Generating keyword ideas for customer 138-688-4705
2025-07-29 16:09:49 - KeywordResearch - ERROR - Unexpected error generating keyword ideas: KeywordPlanIdeaServiceClient.generate_keyword_ideas() got an unexpected keyword argument 'customer_id'
2025-07-29 16:09:49 - KeywordResearch - ERROR - Error during keyword generation: KeywordPlanIdeaServiceClient.generate_keyword_ideas() got an unexpected keyword argument 'customer_id'
2025-07-29 16:10:23 - KeywordResearch - INFO - Application closing...
2025-07-29 16:10:23 - KeywordResearch - INFO - Application closed normally
2025-07-29 16:10:48 - KeywordResearch - INFO - ==================================================
2025-07-29 16:10:48 - KeywordResearch - INFO - Google Ads Keyword Research Tool Starting
2025-07-29 16:10:48 - KeywordResearch - INFO - ==================================================
2025-07-29 16:10:48 - KeywordResearch - INFO - Loading configuration...
2025-07-29 16:10:48 - KeywordResearch - INFO - Configuration loaded successfully
2025-07-29 16:10:48 - KeywordResearch - INFO - Initializing Google Ads service...
2025-07-29 16:10:48 - KeywordResearch - INFO - Google Ads client initialized successfully
2025-07-29 16:10:48 - KeywordResearch - INFO - Google Ads service initialized successfully
2025-07-29 16:10:48 - KeywordResearch - INFO - CSV exporter initialized successfully
2025-07-29 16:10:48 - KeywordResearch - INFO - Application initialized successfully
2025-07-29 16:11:39 - KeywordResearch - INFO - Starting keyword generation...
2025-07-29 16:11:39 - KeywordResearch - INFO - Creating keyword research request...
2025-07-29 16:11:39 - KeywordResearch - INFO - Request validation successful:
2025-07-29 16:11:39 - KeywordResearch - INFO -   - Customer ID: 138-688-4705
2025-07-29 16:11:39 - KeywordResearch - INFO -   - Keywords: 1 provided
2025-07-29 16:11:39 - KeywordResearch - INFO -   - Page URL: No
2025-07-29 16:11:39 - KeywordResearch - INFO -   - Location IDs: 1 provided
2025-07-29 16:11:39 - KeywordResearch - INFO -   - Language ID: 1000
2025-07-29 16:11:39 - KeywordResearch - INFO -   - Seed type: keyword_seed
2025-07-29 16:11:39 - KeywordResearch - INFO -   - Output file: keyword_ideas.csv
2025-07-29 16:11:39 - KeywordResearch - INFO - Generating keyword ideas for customer 138-688-4705...
2025-07-29 16:11:39 - KeywordResearch - INFO - Generating keyword ideas for customer 138-688-4705
2025-07-29 16:11:39 - KeywordResearch - ERROR - Unexpected error generating keyword ideas: KeywordPlanIdeaServiceClient.generate_keyword_ideas() got an unexpected keyword argument 'customer_id'
2025-07-29 16:11:39 - KeywordResearch - ERROR - Error during keyword generation: KeywordPlanIdeaServiceClient.generate_keyword_ideas() got an unexpected keyword argument 'customer_id'
2025-07-29 16:13:50 - KeywordResearch - INFO - Application closing...
2025-07-29 16:13:50 - KeywordResearch - INFO - Application closed normally
2025-07-29 16:14:15 - KeywordResearch - INFO - ==================================================
2025-07-29 16:14:15 - KeywordResearch - INFO - Google Ads Keyword Research Tool Starting
2025-07-29 16:14:15 - KeywordResearch - INFO - ==================================================
2025-07-29 16:14:15 - KeywordResearch - INFO - Loading configuration...
2025-07-29 16:14:15 - KeywordResearch - INFO - Configuration loaded successfully
2025-07-29 16:14:15 - KeywordResearch - INFO - Initializing Google Ads service...
2025-07-29 16:14:15 - KeywordResearch - INFO - Google Ads client initialized successfully
2025-07-29 16:14:15 - KeywordResearch - INFO - Google Ads service initialized successfully
2025-07-29 16:14:15 - KeywordResearch - INFO - CSV exporter initialized successfully
2025-07-29 16:14:15 - KeywordResearch - INFO - Application initialized successfully
2025-07-29 16:15:01 - APITest - INFO - Google Ads client initialized successfully
2025-07-29 16:15:01 - APITest - INFO - Generating keyword ideas for customer 138-688-4705
2025-07-29 16:15:01 - APITest - ERROR - Unexpected error generating keyword ideas: KeywordPlanIdeaServiceClient.generate_keyword_ideas() got an unexpected keyword argument 'customer_id'
2025-07-29 16:18:15 - APITest - INFO - Google Ads client initialized successfully
2025-07-29 16:18:15 - APITest - INFO - Generating keyword ideas for customer 138-688-4705
2025-07-29 16:18:16 - APITest - ERROR - Google Ads API error: (<_InactiveRpcError of RPC that terminated with:
	status = StatusCode.INVALID_ARGUMENT
	details = "Request contains an invalid argument."
	debug_error_string = "UNKNOWN:Error received from peer ipv4:*************:443 {grpc_status:3, grpc_message:"Request contains an invalid argument."}"
>, <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.INVALID_ARGUMENT
	details = "Request contains an invalid argument."
	debug_error_string = "UNKNOWN:Error received from peer ipv4:*************:443 {grpc_status:3, grpc_message:"Request contains an invalid argument."}"
>, errors {
  error_code {
    request_error: INVALID_CUSTOMER_ID
  }
  message: "Invalid customer ID \'138-688-4705\'."
}
request_id: "appRbqz5T_5jb3QWrSS_tA"
, 'appRbqz5T_5jb3QWrSS_tA')
2025-07-29 16:18:42 - APITest - INFO - Google Ads client initialized successfully
2025-07-29 16:18:42 - APITest - INFO - Generating keyword ideas for customer 1386884705
2025-07-29 16:18:43 - APITest - ERROR - Google Ads API error: (<_InactiveRpcError of RPC that terminated with:
	status = StatusCode.PERMISSION_DENIED
	details = "The caller does not have permission"
	debug_error_string = "UNKNOWN:Error received from peer ipv4:*************:443 {grpc_message:"The caller does not have permission", grpc_status:7}"
>, <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.PERMISSION_DENIED
	details = "The caller does not have permission"
	debug_error_string = "UNKNOWN:Error received from peer ipv4:*************:443 {grpc_message:"The caller does not have permission", grpc_status:7}"
>, errors {
  error_code {
    authorization_error: DEVELOPER_TOKEN_NOT_APPROVED
  }
  message: "The developer token is only approved for use with test accounts. To access non-test accounts, apply for Basic or Standard access."
}
request_id: "R_OQyikDRc45RojEFzgtrQ"
, 'R_OQyikDRc45RojEFzgtrQ')
2025-07-29 16:19:14 - KeywordResearch - INFO - ==================================================
2025-07-29 16:19:14 - KeywordResearch - INFO - Google Ads Keyword Research Tool Starting
2025-07-29 16:19:14 - KeywordResearch - INFO - ==================================================
2025-07-29 16:19:14 - KeywordResearch - INFO - Loading configuration...
2025-07-29 16:19:14 - KeywordResearch - INFO - Configuration loaded successfully
2025-07-29 16:19:14 - KeywordResearch - INFO - Initializing Google Ads service...
2025-07-29 16:19:14 - KeywordResearch - INFO - Google Ads client initialized successfully
2025-07-29 16:19:14 - KeywordResearch - INFO - Google Ads service initialized successfully
2025-07-29 16:19:14 - KeywordResearch - INFO - CSV exporter initialized successfully
2025-07-29 16:19:14 - KeywordResearch - INFO - Application initialized successfully
2025-07-29 16:20:02 - KeywordResearch - INFO - ==================================================
2025-07-29 16:20:02 - KeywordResearch - INFO - Google Ads Keyword Research Tool Starting
2025-07-29 16:20:02 - KeywordResearch - INFO - ==================================================
2025-07-29 16:20:02 - KeywordResearch - INFO - Loading configuration...
2025-07-29 16:20:02 - KeywordResearch - INFO - Configuration loaded successfully
2025-07-29 16:20:02 - KeywordResearch - INFO - Initializing Google Ads service...
2025-07-29 16:20:02 - KeywordResearch - INFO - Google Ads client initialized successfully
2025-07-29 16:20:02 - KeywordResearch - INFO - Google Ads service initialized successfully
2025-07-29 16:20:02 - KeywordResearch - INFO - CSV exporter initialized successfully
2025-07-29 16:20:02 - KeywordResearch - INFO - Application initialized successfully
