#!/usr/bin/env python3
"""
Script to list accessible Google Ads customers
"""

import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException

def list_accessible_customers():
    """List all accessible Google Ads customers"""
    try:
        print("Listing accessible Google Ads customers...")
        
        # Initialize the Google Ads client
        config_path = os.path.join(project_root, "google-ads.yaml")
        client = GoogleAdsClient.load_from_storage(path=config_path, version="v20")
        
        # Get the CustomerService
        customer_service = client.get_service("CustomerService")
        
        # List accessible customers
        accessible_customers = customer_service.list_accessible_customers()
        
        print(f"\n✅ Found {len(accessible_customers.resource_names)} accessible customers:")
        print("=" * 60)
        
        for i, customer_resource in enumerate(accessible_customers.resource_names, 1):
            # Extract customer ID from resource name
            customer_id = customer_resource.split('/')[-1]
            print(f"{i}. Customer ID: {customer_id}")
            
            try:
                # Get customer details
                customer = customer_service.get_customer(resource_name=customer_resource)
                print(f"   Name: {customer.descriptive_name}")
                print(f"   Currency: {customer.currency_code}")
                print(f"   Time Zone: {customer.time_zone}")
                print(f"   Test Account: {customer.test_account}")
                print(f"   Manager: {customer.manager}")
                print("-" * 40)
            except Exception as e:
                print(f"   (Could not get details: {str(e)})")
                print("-" * 40)
        
        if not accessible_customers.resource_names:
            print("❌ No accessible customers found.")
            print("\nPossible reasons:")
            print("1. Your OAuth2 credentials don't have access to any accounts")
            print("2. You need to be granted access to Google Ads accounts")
            print("3. Your developer token might not be approved")
        
        return True
        
    except GoogleAdsException as ex:
        print(f"❌ Google Ads API Error:")
        print(f"Request ID: {ex.request_id}")
        for error in ex.failure.errors:
            print(f"Error: {error.message}")
        return False
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = list_accessible_customers()
    sys.exit(0 if success else 1)
