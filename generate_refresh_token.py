from google_auth_oauthlib.flow import InstalledAppFlow

# دي هي الـ SCOPES الخاصة ب Google Ads API
SCOPES = ['https://www.googleapis.com/auth/adwords']

def main():
    # ده المسار بتاع ملف الـ credentials JSON لو انت حملته من Google Cloud Console
    # أو ممكن تحط البيانات يدوي
    flow = InstalledAppFlow.from_client_config(
        {
            "installed": {
                "client_id": "*********************************************.apps.googleusercontent.com",
                "client_secret": "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",
                "redirect_uris": ["urn:ietf:wg:oauth:2.0:oob"],
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token"
            }
        },
        SCOPES
    )

    creds = flow.run_local_server(port=8080, prompt='consent')
    print("\n✅ Your refresh token is:\n")
    print(creds.refresh_token)

if __name__ == '__main__':
    main()
