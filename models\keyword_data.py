"""
Data models for keyword research
"""
from dataclasses import dataclass
from typing import List, Optional, Union
from enum import Enum


class CompetitionLevel(Enum):
    """Competition level enumeration"""
    UNSPECIFIED = "UNSPECIFIED"
    UNKNOWN = "UNKNOWN"
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"


@dataclass
class KeywordIdea:
    """Data class representing a keyword idea"""
    keyword_text: str
    avg_monthly_searches: Optional[int] = None
    competition: Optional[CompetitionLevel] = None
    top_of_page_cpc_low_micros: Optional[int] = None
    top_of_page_cpc_high_micros: Optional[int] = None
    
    @property
    def top_of_page_cpc_low(self) -> Optional[float]:
        """Convert low CPC from micros to currency"""
        if self.top_of_page_cpc_low_micros is not None:
            return self.top_of_page_cpc_low_micros / 1_000_000
        return None
    
    @property
    def top_of_page_cpc_high(self) -> Optional[float]:
        """Convert high CPC from micros to currency"""
        if self.top_of_page_cpc_high_micros is not None:
            return self.top_of_page_cpc_high_micros / 1_000_000
        return None
    
    @property
    def competition_value(self) -> int:
        """Get numeric value for competition level for sorting"""
        competition_values = {
            CompetitionLevel.LOW: 1,
            CompetitionLevel.MEDIUM: 2,
            CompetitionLevel.HIGH: 3,
            CompetitionLevel.UNKNOWN: 4,
            CompetitionLevel.UNSPECIFIED: 5
        }
        return competition_values.get(self.competition, 5)
    
    def to_dict(self) -> dict:
        """Convert to dictionary for CSV export"""
        return {
            "Keyword": self.keyword_text,
            "Avg Monthly Searches": self.avg_monthly_searches or 0,
            "Competition": self.competition.value if self.competition else "UNKNOWN",
            "Top of Page CPC (Low)": f"{self.top_of_page_cpc_low:.2f}" if self.top_of_page_cpc_low else "N/A",
            "Top of Page CPC (High)": f"{self.top_of_page_cpc_high:.2f}" if self.top_of_page_cpc_high else "N/A"
        }


@dataclass
class KeywordResearchRequest:
    """Data class for keyword research request parameters"""
    customer_id: str
    keywords: List[str]
    page_url: Optional[str] = None
    location_ids: List[str] = None
    language_id: str = "1000"  # Default to English
    output_file: str = "keyword_ideas.csv"
    
    def __post_init__(self):
        """Post-initialization processing"""
        if self.location_ids is None:
            self.location_ids = []
        
        # Clean and validate keywords
        self.keywords = [kw.strip() for kw in self.keywords if kw.strip()]
        
        # Clean and validate location IDs
        self.location_ids = [loc.strip() for loc in self.location_ids if loc.strip()]
    
    @property
    def has_keywords(self) -> bool:
        """Check if keywords are provided"""
        return bool(self.keywords)
    
    @property
    def has_url(self) -> bool:
        """Check if URL is provided"""
        return bool(self.page_url and self.page_url.strip())
    
    @property
    def seed_type(self) -> str:
        """Determine the type of seed to use"""
        if self.has_keywords and self.has_url:
            return "keyword_and_url_seed"
        elif self.has_keywords:
            return "keyword_seed"
        elif self.has_url:
            return "url_seed"
        else:
            raise ValueError("Either keywords or URL must be provided")


class KeywordSorter:
    """Utility class for sorting keyword ideas"""
    
    @staticmethod
    def sort_keywords(keywords: List[KeywordIdea]) -> List[KeywordIdea]:
        """
        Sort keywords by priority:
        1. Average monthly searches (descending)
        2. High Top of Page CPC (ascending)
        3. Competition level (ascending)
        
        Args:
            keywords: List of KeywordIdea objects
            
        Returns:
            Sorted list of KeywordIdea objects
        """
        return sorted(
            keywords,
            key=lambda k: (
                -(k.avg_monthly_searches or 0),  # Descending (negative for reverse)
                k.top_of_page_cpc_high or float('inf'),  # Ascending
                k.competition_value  # Ascending
            )
        )
