"""
Google Ads API client service
"""
from typing import List, Optional
from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException

from config.config import Config
from models.keyword_data import Keyword<PERSON><PERSON>, KeywordResearchRequest, CompetitionLevel
from utils.logger import Logger


class GoogleAdsService:
    """Service class for Google Ads API operations"""
    
    def __init__(self, config: Config, logger: Optional[Logger] = None):
        """
        Initialize Google Ads service
        
        Args:
            config: Configuration object
            logger: Logger instance
        """
        self.config = config
        self.logger = logger or Logger("GoogleAdsService")
        self._client = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize Google Ads client"""
        try:
            # Create client configuration
            client_config = {
                'developer_token': self.config.developer_token,
                'client_id': self.config.client_id,
                'client_secret': self.config.client_secret,
                'refresh_token': self.config.refresh_token,
                'use_proto_plus': self.config.use_proto_plus
            }
            
            # Add login customer ID if available
            if self.config.login_customer_id:
                client_config['login_customer_id'] = self.config.login_customer_id
            
            self._client = GoogleAdsClient.load_from_dict(client_config)
            self.logger.info("Google Ads client initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Google Ads client: {str(e)}")
            raise
    
    def generate_keyword_ideas(self, request: KeywordResearchRequest) -> List[KeywordIdea]:
        """
        Generate keyword ideas using Google Ads API
        
        Args:
            request: Keyword research request parameters
            
        Returns:
            List of KeywordIdea objects
        """
        try:
            self.logger.info(f"Generating keyword ideas for customer {request.customer_id}")
            
            # Get the keyword plan idea service
            keyword_plan_idea_service = self._client.get_service("KeywordPlanIdeaService")

            # Build the request
            api_request = self._build_api_request(request)

            # Execute the request using the correct API pattern
            response = keyword_plan_idea_service.generate_keyword_ideas(
                request=api_request
            )
            
            # Process the response
            keyword_ideas = self._process_response(response)
            
            self.logger.info(f"Successfully generated {len(keyword_ideas)} keyword ideas")
            return keyword_ideas
            
        except GoogleAdsException as ex:
            self.logger.error(f"Google Ads API error: {ex}")
            error_details = []
            for error in ex.failure.errors:
                error_details.append(f"Error: {error.message}")
            raise Exception(f"Google Ads API error: {'; '.join(error_details)}")
            
        except Exception as e:
            self.logger.error(f"Unexpected error generating keyword ideas: {str(e)}")
            raise
    
    def _build_api_request(self, request: KeywordResearchRequest):
        """Build the Google Ads API request object"""
        # Create the proper GenerateKeywordIdeasRequest object
        api_request = self._client.get_type("GenerateKeywordIdeasRequest")
        # Remove dashes from customer ID if present
        api_request.customer_id = request.customer_id.replace("-", "")

        # Set language
        if request.language_id:
            api_request.language = self._client.get_service("GoogleAdsService").language_constant_path(
                request.language_id
            )

        # Set geo target constants
        if request.location_ids:
            api_request.geo_target_constants.extend([
                self._client.get_service("GoogleAdsService").geo_target_constant_path(location_id)
                for location_id in request.location_ids
            ])

        # Set keyword plan network
        api_request.keyword_plan_network = (
            self._client.enums.KeywordPlanNetworkEnum.GOOGLE_SEARCH_AND_PARTNERS
        )

        # Set include_adult_keywords to False
        api_request.include_adult_keywords = False

        # Set seed based on type
        if request.seed_type == "keyword_seed":
            api_request.keyword_seed.keywords.extend(request.keywords)
        elif request.seed_type == "url_seed":
            api_request.url_seed.url = request.page_url
        elif request.seed_type == "keyword_and_url_seed":
            api_request.keyword_and_url_seed.url = request.page_url
            api_request.keyword_and_url_seed.keywords.extend(request.keywords)

        return api_request

    def _process_response(self, response) -> List[KeywordIdea]:
        """Process API response and convert to KeywordIdea objects"""
        keyword_ideas = []

        for result in response.results:
            # Extract keyword text
            keyword_text = result.text

            # Extract metrics
            metrics = result.keyword_idea_metrics
            avg_monthly_searches = None
            competition = None
            top_of_page_cpc_low_micros = None
            top_of_page_cpc_high_micros = None

            if metrics:
                avg_monthly_searches = metrics.avg_monthly_searches

                # Convert competition level
                if metrics.competition:
                    competition = self._convert_competition_level(metrics.competition)

                # Extract CPC data
                if metrics.top_of_page_cpc_low_micros:
                    top_of_page_cpc_low_micros = metrics.top_of_page_cpc_low_micros

                if metrics.top_of_page_cpc_high_micros:
                    top_of_page_cpc_high_micros = metrics.top_of_page_cpc_high_micros

            # Create KeywordIdea object
            keyword_idea = KeywordIdea(
                keyword_text=keyword_text,
                avg_monthly_searches=avg_monthly_searches,
                competition=competition,
                top_of_page_cpc_low_micros=top_of_page_cpc_low_micros,
                top_of_page_cpc_high_micros=top_of_page_cpc_high_micros
            )

            keyword_ideas.append(keyword_idea)

        return keyword_ideas

    def _convert_competition_level(self, api_competition) -> CompetitionLevel:
        """Convert API competition level to our enum"""
        # Use the client's enum values
        competition_enum = self._client.enums.KeywordPlanCompetitionLevelEnum.KeywordPlanCompetitionLevel

        competition_mapping = {
            competition_enum.LOW: CompetitionLevel.LOW,
            competition_enum.MEDIUM: CompetitionLevel.MEDIUM,
            competition_enum.HIGH: CompetitionLevel.HIGH,
            competition_enum.UNKNOWN: CompetitionLevel.UNKNOWN,
            competition_enum.UNSPECIFIED: CompetitionLevel.UNSPECIFIED
        }

        return competition_mapping.get(api_competition, CompetitionLevel.UNKNOWN)
