# Google Ads API Configuration Template
# Copy this file to 'google-ads.yaml' and fill in your actual credentials

# Your Google Ads API developer token
developer_token: "YOUR_DEVELOPER_TOKEN_HERE"

# OAuth2 Client ID from Google Cloud Console
client_id: "YOUR_CLIENT_ID_HERE.apps.googleusercontent.com"

# OAuth2 Client Secret from Google Cloud Console
client_secret: "YOUR_CLIENT_SECRET_HERE"

# OAuth2 Refresh Token (generate using generate_refresh_token.py)
refresh_token: "YOUR_REFRESH_TOKEN_HERE"

# Use proto-plus library (recommended)
use_proto_plus: True

# Optional: Login Customer ID (if managing multiple accounts)
# login_customer_id: "YOUR_LOGIN_CUSTOMER_ID"

# Instructions:
# 1. Get a developer token from Google Ads API Center
# 2. Create OAuth2 credentials in Google Cloud Console
# 3. Generate a refresh token using the provided script
# 4. Replace the placeholder values above with your actual credentials
# 5. Save this file as 'google-ads.yaml' (remove .template extension)
