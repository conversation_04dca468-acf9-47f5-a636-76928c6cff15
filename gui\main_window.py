"""
Main GUI window for Google Ads Keyword Research Tool
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
from typing import Optional

from config.config import AppSettings
from utils.logger import GUILogger
from gui.input_frame import InputFrame
from gui.output_frame import Output<PERSON>rame
from services.keyword_research_service import KeywordResearchService


class MainWindow:
    """Main application window"""
    
    def __init__(self):
        """Initialize the main window"""
        self.root = tk.Tk()
        self.setup_window()
        
        # Initialize components
        self.logger = None
        self.research_service = None
        self.input_frame = None
        self.output_frame = None
        
        # State variables
        self.is_processing = False
        
        self.create_widgets()
        self.setup_logging()
    
    def setup_window(self) -> None:
        """Setup main window properties"""
        self.root.title(AppSettings.WINDOW_TITLE)
        self.root.geometry(AppSettings.WINDOW_SIZE)
        self.root.minsize(*AppSettings.WINDOW_MIN_SIZE)
        
        # Center the window
        self.center_window()
        
        # Configure grid weights for responsive design
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
    
    def center_window(self) -> None:
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self) -> None:
        """Create and layout all widgets"""
        # Create main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Create input frame
        self.input_frame = InputFrame(main_frame, self.on_generate_keywords)
        self.input_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        # Create output frame
        self.output_frame = OutputFrame(main_frame)
        self.output_frame.grid(row=1, column=0, sticky="nsew")
        
        # Create status bar
        self.create_status_bar()
    
    def create_status_bar(self) -> None:
        """Create status bar at the bottom"""
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        
        status_frame = ttk.Frame(self.root)
        status_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=(0, 5))
        
        status_label = ttk.Label(status_frame, textvariable=self.status_var, relief="sunken")
        status_label.pack(side="left", fill="x", expand=True)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            status_frame, 
            variable=self.progress_var, 
            mode='indeterminate'
        )
        self.progress_bar.pack(side="right", padx=(10, 0))
    
    def setup_logging(self) -> None:
        """Setup logging with GUI integration"""
        log_text_widget = self.output_frame.get_log_widget()
        self.logger = GUILogger(log_text_widget)
        
        # Initialize research service
        try:
            self.research_service = KeywordResearchService(self.logger)
            self.logger.info("Application initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize application: {str(e)}")
            messagebox.showerror("Initialization Error", f"Failed to initialize application:\n{str(e)}")
    
    def on_generate_keywords(self) -> None:
        """Handle keyword generation request"""
        if self.is_processing:
            messagebox.showwarning("Processing", "Keyword generation is already in progress")
            return
        
        try:
            # Get input data
            request_data = self.input_frame.get_request_data()
            if not request_data:
                return
            
            # Validate inputs
            validation_result = self.input_frame.validate_inputs()
            if not validation_result[0]:
                messagebox.showerror("Validation Error", validation_result[1])
                return
            
            # Start processing in a separate thread
            self.start_processing()
            
            thread = threading.Thread(
                target=self.process_keyword_generation,
                args=(request_data,),
                daemon=True
            )
            thread.start()
            
        except Exception as e:
            self.logger.error(f"Error starting keyword generation: {str(e)}")
            messagebox.showerror("Error", f"Error starting keyword generation:\n{str(e)}")
    
    def process_keyword_generation(self, request_data: dict) -> None:
        """Process keyword generation in background thread"""
        try:
            self.logger.info("Starting keyword generation...")
            
            # Generate keywords
            success = self.research_service.generate_keywords(request_data)
            
            # Update UI in main thread
            self.root.after(0, self.on_processing_complete, success)
            
        except Exception as e:
            self.logger.error(f"Error during keyword generation: {str(e)}")
            self.root.after(0, self.on_processing_error, str(e))
    
    def start_processing(self) -> None:
        """Start processing state"""
        self.is_processing = True
        self.status_var.set("Generating keywords...")
        self.progress_bar.start()
        self.input_frame.set_enabled(False)
    
    def stop_processing(self) -> None:
        """Stop processing state"""
        self.is_processing = False
        self.progress_bar.stop()
        self.input_frame.set_enabled(True)
    
    def on_processing_complete(self, success: bool) -> None:
        """Handle processing completion"""
        self.stop_processing()
        
        if success:
            self.status_var.set("Keyword generation completed successfully")
            self.logger.success("Keyword generation completed successfully")
            messagebox.showinfo("Success", "Keywords generated and exported successfully!")
        else:
            self.status_var.set("Keyword generation failed")
            messagebox.showerror("Error", "Keyword generation failed. Check the log for details.")
    
    def on_processing_error(self, error_message: str) -> None:
        """Handle processing error"""
        self.stop_processing()
        self.status_var.set("Error occurred during processing")
        messagebox.showerror("Error", f"An error occurred:\n{error_message}")
    
    def run(self) -> None:
        """Start the GUI main loop"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.logger.info("Application interrupted by user")
        except Exception as e:
            self.logger.error(f"Unexpected error in main loop: {str(e)}")
        finally:
            self.cleanup()
    
    def cleanup(self) -> None:
        """Cleanup resources before closing"""
        try:
            if self.logger:
                self.logger.info("Application closing...")
        except Exception:
            pass
