"""
CSV export service for keyword research data
"""
import csv
import os
from typing import List
import pandas as pd

from models.keyword_data import Keyword<PERSON><PERSON>, KeywordSorter
from config.config import AppSettings
from utils.logger import Logger


class CSVExporter:
    """Service class for exporting keyword data to CSV"""
    
    def __init__(self, logger: Logger = None):
        """
        Initialize CSV exporter
        
        Args:
            logger: Logger instance
        """
        self.logger = logger or Logger("CSVExporter")
    
    def export_keywords(self, keywords: List[KeywordIdea], output_file: str) -> bool:
        """
        Export keyword ideas to CSV file
        
        Args:
            keywords: List of KeywordIdea objects
            output_file: Path to output CSV file
            
        Returns:
            bool: True if export successful, False otherwise
        """
        try:
            if not keywords:
                self.logger.warning("No keywords to export")
                return False
            
            # Sort keywords according to priority
            sorted_keywords = KeywordSorter.sort_keywords(keywords)
            
            # Create directory if it doesn't exist
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # Export using pandas for better handling
            self._export_with_pandas(sorted_keywords, output_file)
            
            self.logger.info(f"Successfully exported {len(sorted_keywords)} keywords to {output_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error exporting keywords to CSV: {str(e)}")
            return False
    
    def _export_with_pandas(self, keywords: List[KeywordIdea], output_file: str) -> None:
        """
        Export keywords using pandas DataFrame
        
        Args:
            keywords: List of KeywordIdea objects
            output_file: Path to output CSV file
        """
        # Convert keywords to dictionaries
        data = [keyword.to_dict() for keyword in keywords]
        
        # Create DataFrame
        df = pd.DataFrame(data)
        
        # Ensure columns are in the correct order
        df = df[AppSettings.CSV_HEADERS]
        
        # Export to CSV with proper encoding and no index
        df.to_csv(
            output_file,
            index=False,
            encoding=AppSettings.CSV_ENCODING,
            lineterminator='\n'  # Prevent extra blank lines
        )
    
    def _export_with_csv_writer(self, keywords: List[KeywordIdea], output_file: str) -> None:
        """
        Alternative export method using csv.writer (fallback)
        
        Args:
            keywords: List of KeywordIdea objects
            output_file: Path to output CSV file
        """
        with open(output_file, 'w', newline='', encoding=AppSettings.CSV_ENCODING) as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=AppSettings.CSV_HEADERS)
            
            # Write header
            writer.writeheader()
            
            # Write data
            for keyword in keywords:
                writer.writerow(keyword.to_dict())
    
    def validate_output_path(self, output_file: str) -> tuple[bool, str]:
        """
        Validate the output file path
        
        Args:
            output_file: Path to validate
            
        Returns:
            tuple: (is_valid, error_message)
        """
        try:
            # Check if path is empty
            if not output_file or not output_file.strip():
                return False, "Output file path cannot be empty"
            
            # Check file extension
            if not output_file.lower().endswith('.csv'):
                return False, "Output file must have .csv extension"
            
            # Check if directory exists or can be created
            output_dir = os.path.dirname(output_file)
            if output_dir:
                if not os.path.exists(output_dir):
                    try:
                        os.makedirs(output_dir, exist_ok=True)
                    except Exception as e:
                        return False, f"Cannot create directory: {str(e)}"
                
                if not os.access(output_dir, os.W_OK):
                    return False, "No write permission for the directory"
            
            # Check if file exists and is writable
            if os.path.exists(output_file):
                if not os.access(output_file, os.W_OK):
                    return False, "No write permission for the file"
            
            return True, ""
            
        except Exception as e:
            return False, f"Invalid file path: {str(e)}"
    
    def get_file_info(self, output_file: str) -> dict:
        """
        Get information about the output file
        
        Args:
            output_file: Path to the file
            
        Returns:
            dict: File information
        """
        info = {
            'exists': False,
            'size': 0,
            'writable': False,
            'directory_exists': False
        }
        
        try:
            if os.path.exists(output_file):
                info['exists'] = True
                info['size'] = os.path.getsize(output_file)
                info['writable'] = os.access(output_file, os.W_OK)
            
            output_dir = os.path.dirname(output_file)
            if output_dir:
                info['directory_exists'] = os.path.exists(output_dir)
            else:
                info['directory_exists'] = True  # Current directory
                
        except Exception:
            pass
        
        return info
