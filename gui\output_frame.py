"""
Output frame for Google Ads Keyword Research Tool
"""
import tkinter as tk
from tkinter import ttk, scrolledtext


class OutputFrame(ttk.LabelFrame):
    """Output frame for displaying logs and results"""
    
    def __init__(self, parent):
        """
        Initialize output frame
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent, text="Output Log", padding="10")
        
        self.create_widgets()
    
    def create_widgets(self) -> None:
        """Create and layout output widgets"""
        # Configure grid weights for responsive design
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self)
        self.notebook.grid(row=0, column=0, sticky="nsew")
        
        # Log tab
        self.create_log_tab()
        
        # Results tab (for future enhancement)
        self.create_results_tab()
    
    def create_log_tab(self) -> None:
        """Create the log display tab"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="Log")
        
        # Configure grid weights
        log_frame.grid_rowconfigure(0, weight=1)
        log_frame.grid_columnconfigure(0, weight=1)
        
        # Create scrolled text widget for log display
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            wrap=tk.WORD,
            width=80,
            height=15,
            font=("Consolas", 9),
            state=tk.DISABLED
        )
        self.log_text.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        
        # Configure text tags for different log levels
        self.log_text.tag_configure("INFO", foreground="black")
        self.log_text.tag_configure("WARNING", foreground="orange")
        self.log_text.tag_configure("ERROR", foreground="red")
        self.log_text.tag_configure("SUCCESS", foreground="green")
        
        # Create control buttons frame
        control_frame = ttk.Frame(log_frame)
        control_frame.grid(row=1, column=0, sticky="ew", pady=(5, 0))
        
        # Clear log button
        clear_button = ttk.Button(
            control_frame,
            text="Clear Log",
            command=self.clear_log
        )
        clear_button.pack(side="left")
        
        # Save log button
        save_button = ttk.Button(
            control_frame,
            text="Save Log",
            command=self.save_log
        )
        save_button.pack(side="left", padx=(10, 0))
        
        # Auto-scroll checkbox
        self.auto_scroll_var = tk.BooleanVar(value=True)
        auto_scroll_check = ttk.Checkbutton(
            control_frame,
            text="Auto-scroll",
            variable=self.auto_scroll_var
        )
        auto_scroll_check.pack(side="right")
    
    def create_results_tab(self) -> None:
        """Create the results display tab"""
        results_frame = ttk.Frame(self.notebook)
        self.notebook.add(results_frame, text="Results")
        
        # Configure grid weights
        results_frame.grid_rowconfigure(0, weight=1)
        results_frame.grid_columnconfigure(0, weight=1)
        
        # Create treeview for results display
        self.results_tree = ttk.Treeview(
            results_frame,
            columns=("keyword", "searches", "competition", "cpc_low", "cpc_high"),
            show="headings",
            height=15
        )
        
        # Configure column headings
        self.results_tree.heading("keyword", text="Keyword")
        self.results_tree.heading("searches", text="Avg Monthly Searches")
        self.results_tree.heading("competition", text="Competition")
        self.results_tree.heading("cpc_low", text="CPC Low")
        self.results_tree.heading("cpc_high", text="CPC High")
        
        # Configure column widths
        self.results_tree.column("keyword", width=200)
        self.results_tree.column("searches", width=120)
        self.results_tree.column("competition", width=100)
        self.results_tree.column("cpc_low", width=80)
        self.results_tree.column("cpc_high", width=80)
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(results_frame, orient="horizontal", command=self.results_tree.xview)
        
        self.results_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.results_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Results info frame
        info_frame = ttk.Frame(results_frame)
        info_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(5, 0))
        
        self.results_info_var = tk.StringVar(value="No results to display")
        results_info_label = ttk.Label(info_frame, textvariable=self.results_info_var)
        results_info_label.pack(side="left")
    
    def get_log_widget(self) -> scrolledtext.ScrolledText:
        """
        Get the log text widget for external logging
        
        Returns:
            ScrolledText widget for logging
        """
        return self.log_text
    
    def clear_log(self) -> None:
        """Clear the log display"""
        self.log_text.configure(state=tk.NORMAL)
        self.log_text.delete("1.0", tk.END)
        self.log_text.configure(state=tk.DISABLED)
    
    def save_log(self) -> None:
        """Save the log to a file"""
        from tkinter import filedialog
        
        filename = filedialog.asksaveasfilename(
            title="Save Log File",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    log_content = self.log_text.get("1.0", tk.END)
                    f.write(log_content)
                
                # Add success message to log
                self.log_text.configure(state=tk.NORMAL)
                self.log_text.insert(tk.END, f"Log saved to: {filename}\n")
                self.log_text.configure(state=tk.DISABLED)
                
                if self.auto_scroll_var.get():
                    self.log_text.see(tk.END)
                    
            except Exception as e:
                from tkinter import messagebox
                messagebox.showerror("Error", f"Failed to save log file:\n{str(e)}")
    
    def update_results(self, keywords_data: list) -> None:
        """
        Update the results display
        
        Args:
            keywords_data: List of keyword data dictionaries
        """
        # Clear existing results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # Add new results
        for keyword_data in keywords_data:
            self.results_tree.insert("", "end", values=(
                keyword_data.get("Keyword", ""),
                keyword_data.get("Avg Monthly Searches", ""),
                keyword_data.get("Competition", ""),
                keyword_data.get("Top of Page CPC (Low)", ""),
                keyword_data.get("Top of Page CPC (High)", "")
            ))
        
        # Update info
        count = len(keywords_data)
        self.results_info_var.set(f"Displaying {count} keyword{'s' if count != 1 else ''}")
        
        # Switch to results tab
        self.notebook.select(1)
    
    def clear_results(self) -> None:
        """Clear the results display"""
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        self.results_info_var.set("No results to display")
