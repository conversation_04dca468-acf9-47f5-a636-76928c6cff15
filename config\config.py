"""
Configuration module for Google Ads Keyword Research Tool
"""
import os
import yaml
from typing import Dict, Any


class Config:
    """Configuration manager for the application"""
    
    def __init__(self, config_file: str = "google-ads.yaml"):
        """
        Initialize configuration
        
        Args:
            config_file (str): Path to the Google Ads configuration file
        """
        self.config_file = config_file
        self._config_data = None
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from YAML file"""
        try:
            if not os.path.exists(self.config_file):
                raise FileNotFoundError(f"Configuration file {self.config_file} not found")
            
            with open(self.config_file, 'r', encoding='utf-8') as file:
                self._config_data = yaml.safe_load(file)
                
            # Validate required fields
            required_fields = ['developer_token', 'client_id', 'client_secret', 'refresh_token']
            for field in required_fields:
                if field not in self._config_data:
                    raise ValueError(f"Missing required configuration field: {field}")
                    
        except Exception as e:
            raise Exception(f"Error loading configuration: {str(e)}")
    
    @property
    def developer_token(self) -> str:
        """Get developer token"""
        return self._config_data.get('developer_token', '')
    
    @property
    def client_id(self) -> str:
        """Get client ID"""
        return self._config_data.get('client_id', '')
    
    @property
    def client_secret(self) -> str:
        """Get client secret"""
        return self._config_data.get('client_secret', '')
    
    @property
    def refresh_token(self) -> str:
        """Get refresh token"""
        return self._config_data.get('refresh_token', '')
    
    @property
    def use_proto_plus(self) -> bool:
        """Get use_proto_plus setting"""
        return self._config_data.get('use_proto_plus', True)
    
    @property
    def login_customer_id(self) -> str:
        """Get login customer ID if available"""
        return self._config_data.get('login_customer_id', '')
    
    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration data"""
        return self._config_data.copy() if self._config_data else {}


# Default application settings
class AppSettings:
    """Application-wide settings and constants"""
    
    # Default values
    DEFAULT_LANGUAGE_ID = "1000"  # English
    DEFAULT_OUTPUT_FILENAME = "keyword_ideas.csv"
    
    # GUI settings
    WINDOW_TITLE = "Google Ads Keyword Research Tool"
    WINDOW_SIZE = "800x700"
    WINDOW_MIN_SIZE = (600, 500)
    
    # Logging settings
    LOG_FILENAME = "keyword_research.log"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
    
    # API settings
    MAX_KEYWORDS_PER_REQUEST = 100
    DEFAULT_PAGE_SIZE = 10000
    
    # CSV export settings
    CSV_ENCODING = "utf-8"
    CSV_HEADERS = [
        "Keyword",
        "Avg Monthly Searches",
        "Competition",
        "Top of Page CPC (Low)",
        "Top of Page CPC (High)"
    ]
