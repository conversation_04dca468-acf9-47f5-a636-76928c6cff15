# Google Ads Keyword Research Tool

A professional GUI application for generating keyword ideas using the Google Ads API. This tool provides a user-friendly interface for keyword research with advanced sorting and CSV export capabilities.

## Features

- **OAuth2 Authentication**: Secure authentication using Google Ads API credentials
- **Multiple Seed Types**: Support for keyword seeds, URL seeds, and combined keyword+URL seeds
- **Advanced Sorting**: Keywords sorted by monthly searches, CPC, and competition level
- **CSV Export**: Professional CSV export with UTF-8 encoding
- **Real-time Logging**: Comprehensive logging with GUI display
- **Error Handling**: Robust error handling and user feedback
- **Professional GUI**: Clean, responsive interface built with tkinter

## Project Structure

```
WordpressProject/
├── main.py                          # Application entry point
├── requirements.txt                 # Python dependencies
├── google-ads.yaml                  # Google Ads API configuration
├── README.md                        # This file
├── config/
│   ├── __init__.py
│   └── config.py                    # Configuration management
├── models/
│   ├── __init__.py
│   └── keyword_data.py              # Data models and structures
├── services/
│   ├── __init__.py
│   ├── google_ads_client.py         # Google Ads API client
│   ├── csv_exporter.py              # CSV export functionality
│   └── keyword_research_service.py  # Main business logic
├── gui/
│   ├── __init__.py
│   ├── main_window.py               # Main application window
│   ├── input_frame.py               # Input form components
│   └── output_frame.py              # Output display components
└── utils/
    ├── __init__.py
    └── logger.py                    # Logging utilities
```

## Prerequisites

1. **Python 3.8+** installed on your system
2. **Google Ads API access** with the following:
   - Developer token
   - Client ID and Client Secret (OAuth2)
   - Refresh token
   - Customer ID for the Google Ads account

## Installation

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Google Ads API**:
   The `google-ads.yaml` file should already be configured with your credentials:
   ```yaml
   developer_token: "your_developer_token"
   client_id: "your_client_id"
   client_secret: "your_client_secret"
   refresh_token: "your_refresh_token"
   use_proto_plus: True
   ```

## Usage

1. **Start the Application**:
   ```bash
   python main.py
   ```

2. **Fill in the Required Fields**:
   - **Customer ID**: Your Google Ads customer ID (without dashes)
   - **Keywords**: Enter seed keywords (one per line or comma-separated)
   - **Page URL**: Optional webpage URL for URL-based keyword ideas
   - **Location IDs**: Optional geographical targeting (location criterion IDs)
   - **Language ID**: Language criterion ID (default: 1000 for English)
   - **Output File**: Choose where to save the CSV results

3. **Generate Keywords**:
   - Click "Generate Keyword Ideas" to start the process
   - Monitor progress in the output log
   - Results will be automatically saved to your specified CSV file

## Input Options

### Seed Types
The application automatically determines the seed type based on your inputs:

- **Keyword Seed**: When only keywords are provided
- **URL Seed**: When only a page URL is provided
- **Keyword and URL Seed**: When both keywords and URL are provided

### Location IDs
Common location criterion IDs:
- `2840` - United States
- `2826` - United Kingdom
- `2124` - Canada
- `2036` - Australia

### Language IDs
Common language criterion IDs:
- `1000` - English
- `1003` - Spanish
- `1002` - French
- `1001` - German

## Output

The application generates a CSV file with the following columns:
- **Keyword**: The keyword text
- **Avg Monthly Searches**: Average monthly search volume
- **Competition**: Competition level (LOW, MEDIUM, HIGH)
- **Top of Page CPC (Low)**: Lower range of top-of-page cost-per-click
- **Top of Page CPC (High)**: Higher range of top-of-page cost-per-click

### Sorting Priority
Results are sorted by:
1. Average monthly searches (descending)
2. High top-of-page CPC (ascending)
3. Competition level (ascending)

## Logging

The application provides comprehensive logging:
- **GUI Log**: Real-time log display in the application
- **File Log**: Persistent logging to `keyword_research.log`
- **Log Levels**: INFO, WARNING, ERROR, SUCCESS

## Error Handling

The application includes robust error handling for:
- Google Ads API errors
- Network connectivity issues
- Invalid input validation
- File I/O operations
- Authentication problems

## Troubleshooting

### Common Issues

1. **"Configuration file not found"**:
   - Ensure `google-ads.yaml` exists in the project root
   - Verify the file contains all required credentials

2. **"Google Ads API error"**:
   - Check your developer token is valid
   - Verify the customer ID is correct
   - Ensure your refresh token hasn't expired

3. **"No keyword ideas generated"**:
   - Try different seed keywords
   - Check if location/language targeting is too restrictive
   - Verify the page URL is accessible

4. **Import errors**:
   - Run `pip install -r requirements.txt`
   - Ensure you're using Python 3.8+

### Getting Help

Check the application log file (`keyword_research.log`) for detailed error information. The log contains timestamps and detailed error messages to help diagnose issues.

## License

This project is provided as-is for educational and research purposes.

## Support

For issues related to the Google Ads API, consult the [official documentation](https://developers.google.com/google-ads/api/docs).
